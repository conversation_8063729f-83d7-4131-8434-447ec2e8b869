class SVGCharts {
    constructor() {
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#48bb78',
            warning: '#ed8936',
            error: '#f56565',
            info: '#4299e1'
        };
        
        this.piscineColors = {
            'Module': '#4C51BF',  // Add Module with a distinct color
            'Piscine Go': '#00ADD8',
            'Piscine JS': '#F7DF1E',
            'Piscine UX': '#FF6B6B',
            'Piscine UI': '#4ECDC4',
            'Piscine Rust': '#CE422B'
        };
    }

    // Create Performance Analytics Chart (Bar Chart)
    createPerformanceAnalyticsChart(piscineData, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;



        const width = 500;
        const height = 350;
        const margin = { top: 20, right: 30, bottom: 80, left: 60 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        // Prepare data - ensure all categories are included
        // Convert object to array and sort to ensure consistent order
        const data = Object.entries(piscineData)
            .map(([name, xp]) => ({ name, xp }))
            .sort((a, b) => {
                // Put Module first, then sort others alphabetically
                if (a.name === 'Module') return -1;
                if (b.name === 'Module') return 1;
                return a.name.localeCompare(b.name);
            });
        
        console.log('Prepared chart data array:', data);
        
        const maxXP = Math.max(...data.map(d => d.xp));
        const barWidth = chartWidth / data.length * 0.8;
        const barSpacing = chartWidth / data.length * 0.2;

        // Create SVG
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', width);
        svg.setAttribute('height', height);
        svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

        // Create chart group
        const chartGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        chartGroup.setAttribute('transform', `translate(${margin.left}, ${margin.top})`);

        // Draw bars
        data.forEach((d, i) => {
            const barHeight = maxXP > 0 ? (d.xp / maxXP) * chartHeight : 0;
            const x = i * (barWidth + barSpacing) + barSpacing / 2;
            const y = chartHeight - barHeight;

            console.log(`Drawing bar for ${d.name} with XP: ${d.xp}, height: ${barHeight}`);

            // Create bar
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x);
            rect.setAttribute('y', y);
            rect.setAttribute('width', barWidth);
            rect.setAttribute('height', barHeight);
            rect.setAttribute('fill', this.piscineColors[d.name] || this.colors.primary);
            rect.setAttribute('class', 'bar');
            rect.setAttribute('rx', '4');

            // Add hover effect
            rect.addEventListener('mouseenter', () => {
                rect.setAttribute('opacity', '0.8');
                this.showTooltip(`${d.name}: ${d.xp.toLocaleString()} XP`, rect);
            });
            rect.addEventListener('mouseleave', () => {
                rect.setAttribute('opacity', '1');
                this.hideTooltip();
            });

            chartGroup.appendChild(rect);

            // Add value label on top of bar
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', x + barWidth / 2);
            text.setAttribute('y', y - 5);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('class', 'axis-label');
            text.setAttribute('font-weight', 'bold');
            text.textContent = d.xp.toLocaleString();
            chartGroup.appendChild(text);

            // Add x-axis labels
            const xLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            xLabel.setAttribute('x', x + barWidth / 2);
            xLabel.setAttribute('y', chartHeight + 20);
            xLabel.setAttribute('text-anchor', 'middle');
            xLabel.setAttribute('class', 'axis-label');
            xLabel.setAttribute('transform', `rotate(-45, ${x + barWidth / 2}, ${chartHeight + 20})`);
            xLabel.textContent = d.name;
            chartGroup.appendChild(xLabel);
        });

        // Draw Y-axis
        const yAxis = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        yAxis.setAttribute('x1', 0);
        yAxis.setAttribute('y1', 0);
        yAxis.setAttribute('x2', 0);
        yAxis.setAttribute('y2', chartHeight);
        yAxis.setAttribute('stroke', '#ccc');
        yAxis.setAttribute('stroke-width', '2');
        chartGroup.appendChild(yAxis);

        // Draw X-axis
        const xAxis = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        xAxis.setAttribute('x1', 0);
        xAxis.setAttribute('y1', chartHeight);
        xAxis.setAttribute('x2', chartWidth);
        xAxis.setAttribute('y2', chartHeight);
        xAxis.setAttribute('stroke', '#ccc');
        xAxis.setAttribute('stroke-width', '2');
        chartGroup.appendChild(xAxis);

        // Add Y-axis label
        const yAxisLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yAxisLabel.setAttribute('x', -30);
        yAxisLabel.setAttribute('y', chartHeight / 2);
        yAxisLabel.setAttribute('text-anchor', 'middle');
        yAxisLabel.setAttribute('class', 'axis-label');
        yAxisLabel.setAttribute('transform', `rotate(-90, -30, ${chartHeight / 2})`);
        yAxisLabel.textContent = 'XP Amount';
        chartGroup.appendChild(yAxisLabel);

        // Add Y-axis ticks
        const tickCount = 5;
        for (let i = 0; i <= tickCount; i++) {
            const tickValue = (maxXP / tickCount) * i;
            const tickY = chartHeight - (tickValue / maxXP) * chartHeight;

            const tick = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            tick.setAttribute('x1', -5);
            tick.setAttribute('y1', tickY);
            tick.setAttribute('x2', 0);
            tick.setAttribute('y2', tickY);
            tick.setAttribute('stroke', '#ccc');
            chartGroup.appendChild(tick);

            const tickLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            tickLabel.setAttribute('x', -10);
            tickLabel.setAttribute('y', tickY + 4);
            tickLabel.setAttribute('text-anchor', 'end');
            tickLabel.setAttribute('class', 'axis-label');
            tickLabel.textContent = Math.round(tickValue).toLocaleString();
            chartGroup.appendChild(tickLabel);
        }

        svg.appendChild(chartGroup);
        container.innerHTML = '';
        container.appendChild(svg);
    }

    // Create XP Progress Chart (Line Chart)
    createXPProgressChart(progressData, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const width = 500;
        const height = 350;
        const margin = { top: 20, right: 30, bottom: 80, left: 70 }; // Increased bottom and left margins for month labels
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        if (!progressData || progressData.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #666;">No XP progress data available</p>';
            return;
        }

        // Prepare data and group by months
        const maxXP = Math.max(...progressData.map(d => d.xp));

        // Group data by months and get the latest XP for each month
        const monthlyData = this.groupDataByMonths(progressData);
        console.log('Monthly XP data:', monthlyData);

        // Create SVG
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', width);
        svg.setAttribute('height', height);
        svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

        // Create chart group
        const chartGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        chartGroup.setAttribute('transform', `translate(${margin.left}, ${margin.top})`);

        // Create path for line using monthly data
        let pathData = '';
        const monthSpacing = chartWidth / (monthlyData.length - 1 || 1);

        monthlyData.forEach((d, i) => {
            const x = i * monthSpacing;
            const y = chartHeight - (d.xp / maxXP) * chartHeight;

            if (i === 0) {
                pathData += `M ${x} ${y}`;
            } else {
                pathData += ` L ${x} ${y}`;
            }
        });

        // Draw line
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', pathData);
        path.setAttribute('stroke', this.colors.primary);
        path.setAttribute('stroke-width', '3');
        path.setAttribute('fill', 'none');
        path.setAttribute('class', 'line');
        chartGroup.appendChild(path);

        // Draw points
        monthlyData.forEach((d, i) => {
            const x = i * monthSpacing;
            const y = chartHeight - (d.xp / maxXP) * chartHeight;

            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', x);
            circle.setAttribute('cy', y);
            circle.setAttribute('r', '4');
            circle.setAttribute('fill', this.colors.secondary);
            circle.setAttribute('stroke', 'white');
            circle.setAttribute('stroke-width', '2');

            // Add hover effect
            circle.addEventListener('mouseenter', () => {
                circle.setAttribute('r', '6');
                this.showTooltip(`${d.monthLabel}: ${d.xp.toLocaleString()} XP`, circle);
            });
            circle.addEventListener('mouseleave', () => {
                circle.setAttribute('r', '4');
                this.hideTooltip();
            });

            chartGroup.appendChild(circle);
        });

        // Draw axes
        const yAxis = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        yAxis.setAttribute('x1', 0);
        yAxis.setAttribute('y1', 0);
        yAxis.setAttribute('x2', 0);
        yAxis.setAttribute('y2', chartHeight);
        yAxis.setAttribute('stroke', '#ccc');
        yAxis.setAttribute('stroke-width', '2');
        chartGroup.appendChild(yAxis);

        const xAxis = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        xAxis.setAttribute('x1', 0);
        xAxis.setAttribute('y1', chartHeight);
        xAxis.setAttribute('x2', chartWidth);
        xAxis.setAttribute('y2', chartHeight);
        xAxis.setAttribute('stroke', '#ccc');
        xAxis.setAttribute('stroke-width', '2');
        chartGroup.appendChild(xAxis);

        // Add Y-axis ticks and labels
        const tickCount = 5;
        for (let i = 0; i <= tickCount; i++) {
            const tickValue = (maxXP / tickCount) * i;
            const tickY = chartHeight - (tickValue / maxXP) * chartHeight;

            const tick = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            tick.setAttribute('x1', -5);
            tick.setAttribute('y1', tickY);
            tick.setAttribute('x2', 0);
            tick.setAttribute('y2', tickY);
            tick.setAttribute('stroke', '#ccc');
            chartGroup.appendChild(tick);

            const tickLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            tickLabel.setAttribute('x', -10);
            tickLabel.setAttribute('y', tickY + 4);
            tickLabel.setAttribute('text-anchor', 'end');
            tickLabel.setAttribute('class', 'axis-label');
            tickLabel.textContent = Math.round(tickValue).toLocaleString();
            chartGroup.appendChild(tickLabel);
        }

        // Add month labels on X-axis
        monthlyData.forEach((d, i) => {
            const x = i * monthSpacing;

            // Add tick mark
            const tick = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            tick.setAttribute('x1', x);
            tick.setAttribute('y1', chartHeight);
            tick.setAttribute('x2', x);
            tick.setAttribute('y2', chartHeight + 5);
            tick.setAttribute('stroke', '#ccc');
            chartGroup.appendChild(tick);

            // Add month label
            const monthLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            monthLabel.setAttribute('x', x);
            monthLabel.setAttribute('y', chartHeight + 20);
            monthLabel.setAttribute('text-anchor', 'middle');
            monthLabel.setAttribute('class', 'axis-label');
            monthLabel.setAttribute('transform', `rotate(-45, ${x}, ${chartHeight + 20})`);
            monthLabel.textContent = d.monthLabel;
            chartGroup.appendChild(monthLabel);
        });

        // Add axis labels
        const yAxisLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yAxisLabel.setAttribute('x', -40);
        yAxisLabel.setAttribute('y', chartHeight / 2);
        yAxisLabel.setAttribute('text-anchor', 'middle');
        yAxisLabel.setAttribute('class', 'axis-label');
        yAxisLabel.setAttribute('transform', `rotate(-90, -40, ${chartHeight / 2})`);
        yAxisLabel.textContent = 'XP Points';
        chartGroup.appendChild(yAxisLabel);

        const xAxisLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        xAxisLabel.setAttribute('x', chartWidth / 2);
        xAxisLabel.setAttribute('y', chartHeight + 60);
        xAxisLabel.setAttribute('text-anchor', 'middle');
        xAxisLabel.setAttribute('class', 'axis-label');
        xAxisLabel.textContent = 'Months';
        chartGroup.appendChild(xAxisLabel);

        svg.appendChild(chartGroup);
        container.innerHTML = '';
        container.appendChild(svg);
    }

    // Group XP progress data by months
    groupDataByMonths(progressData) {
        const monthlyMap = new Map();

        progressData.forEach(d => {
            const date = new Date(d.date);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthLabel = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });

            // Keep the highest XP for each month (since it's cumulative)
            if (!monthlyMap.has(monthKey) || monthlyMap.get(monthKey).xp < d.xp) {
                monthlyMap.set(monthKey, {
                    monthKey,
                    monthLabel,
                    xp: d.xp,
                    date: date
                });
            }
        });

        // Convert to array and sort by date
        const monthlyData = Array.from(monthlyMap.values()).sort((a, b) => a.date - b.date);

        console.log('Grouped monthly data:', monthlyData);
        return monthlyData;
    }

    // Tooltip functions
    showTooltip(text, element) {
        // Remove existing tooltip
        this.hideTooltip();

        const tooltip = document.createElement('div');
        tooltip.id = 'chart-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            white-space: nowrap;
        `;
        tooltip.textContent = text;

        document.body.appendChild(tooltip);

        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
    }

    hideTooltip() {
        const tooltip = document.getElementById('chart-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    // Create Skills Radar Chart
    createSkillsRadarChart(skillsData, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const size = 240;
        const center = size / 2;
        const maxRadius = 90;
        const levels = 6;

        // Skills in exact order from your image: Prog at top, then clockwise
        const skillNames = ['Prog', 'Go', 'Ux', 'Back-End', 'Front-End', 'Js'];

        // Default skills data if none provided - more varied to show spread
        const defaultSkills = {
            'Prog': 85,
            'Go': 40,
            'Js': 75,
            'Front-End': 30,
            'Back-End': 60,
            'Ux': 25
        };

        const skills = { ...defaultSkills, ...skillsData };
        const maxValue = 100; // Fixed scale 0-100

        // Create SVG
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', size);
        svg.setAttribute('height', size);
        svg.setAttribute('viewBox', `0 0 ${size} ${size}`);

        // Create background circles (grid) - more prominent
        for (let i = 1; i <= levels; i++) {
            const radius = (maxRadius / levels) * i;
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', center);
            circle.setAttribute('cy', center);
            circle.setAttribute('r', radius);
            circle.setAttribute('stroke', '#555');
            circle.setAttribute('stroke-width', '1');
            circle.setAttribute('fill', 'none');
            circle.setAttribute('opacity', '0.6');
            svg.appendChild(circle);
        }

        // Create axis lines and labels
        const angleStep = (2 * Math.PI) / skillNames.length;
        skillNames.forEach((skill, index) => {
            const angle = (index * angleStep) - (Math.PI / 2); // Start from top (Prog)
            const x2 = center + Math.cos(angle) * maxRadius;
            const y2 = center + Math.sin(angle) * maxRadius;

            // Axis line
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', center);
            line.setAttribute('y1', center);
            line.setAttribute('x2', x2);
            line.setAttribute('y2', y2);
            line.setAttribute('stroke', '#555');
            line.setAttribute('stroke-width', '1');
            line.setAttribute('opacity', '0.6');
            svg.appendChild(line);

            // Label - positioned outside the circle
            const labelRadius = maxRadius + 25;
            const labelX = center + Math.cos(angle) * labelRadius;
            const labelY = center + Math.sin(angle) * labelRadius;

            const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            label.setAttribute('x', labelX);
            label.setAttribute('y', labelY + 5); // Slight vertical adjustment
            label.setAttribute('text-anchor', 'middle');
            label.setAttribute('fill', '#ccc');
            label.setAttribute('font-family', 'monospace');
            label.setAttribute('font-size', '14');
            label.setAttribute('font-weight', '400');
            label.textContent = skill;
            svg.appendChild(label);
        });

        // Create data area
        let pathData = '';
        const points = [];

        skillNames.forEach((skill, index) => {
            const angle = (index * angleStep) - (Math.PI / 2);
            const value = skills[skill] || 0;
            const radius = (value / maxValue) * maxRadius;
            const x = center + Math.cos(angle) * radius;
            const y = center + Math.sin(angle) * radius;

            points.push({ x, y, value, skill });

            if (index === 0) {
                pathData += `M ${x} ${y}`;
            } else {
                pathData += ` L ${x} ${y}`;
            }
        });

        // Close the path
        if (points.length > 0) {
            pathData += ' Z';
        }

        // Create the filled area - matching your image's purple color
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', pathData);
        path.setAttribute('fill', 'rgba(147, 112, 219, 0.7)'); // Medium purple with transparency
        path.setAttribute('stroke', '#9370DB'); // Medium purple border
        path.setAttribute('stroke-width', '2');
        svg.appendChild(path);

        // Create points at vertices
        points.forEach(point => {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', point.x);
            circle.setAttribute('cy', point.y);
            circle.setAttribute('r', 3);
            circle.setAttribute('fill', '#9370DB');
            circle.setAttribute('stroke', '#fff');
            circle.setAttribute('stroke-width', '1');

            // Add hover effect
            circle.addEventListener('mouseenter', () => {
                circle.setAttribute('r', 5);
                this.showTooltip(`${point.skill}: ${point.value}%`, circle);
            });
            circle.addEventListener('mouseleave', () => {
                circle.setAttribute('r', 3);
                this.hideTooltip();
            });

            svg.appendChild(circle);
        });

        container.innerHTML = '';
        container.appendChild(svg);
    }
}

// Create global charts instance
const svgCharts = new SVGCharts();

// Make the charts instance available globally
window.svgCharts = svgCharts;
