<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL Profile Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Login Page -->
    <div id="login-page" class="page active">
        <div class="login-container">
            <h1>Login to Your Profile</h1>
            <form id="login-form">
                <div class="form-group">
                    <label for="username">Username or Email:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" id="login-btn">Login</button>
                <div id="login-error" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- Profile Dashboard -->
    <div id="profile-page" class="page">
        <header class="header">
            <h1>Profile Dashboard</h1>
            <div class="header-buttons">
                <button id="logout-btn" class="logout-btn">Logout</button>
            </div>
        </header>

        <main class="dashboard">
            <!-- User Info Section -->
            <section class="user-info">
                <h2>User Information</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h3>Basic Info</h3>
                        <div id="user-basic-info">
                            <p><strong>ID:</strong> <span id="user-id">-</span></p>
                            <p><strong>Username:</strong> <span id="user-login">-</span></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <h3>XP Summary</h3>
                        <div id="xp-summary">
                            <p><strong>Total XP:</strong> <span id="total-xp">-</span></p>
                            <p><strong>Projects Completed:</strong> <span id="projects-completed">-</span></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <h3>Audits</h3>
                        <div id="audit-stats">
                            <p><strong>Pass Rate:</strong> <span id="pass-rate">-</span></p>
                            <p><strong>Audit Ratio:</strong> <span id="audit-ratio">-</span></p>
                            <p><strong>Total Audits:</strong> <span id="total-audits">-</span></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="statistics">
                <h2>Statistics & Analytics</h2>
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>XP Distribution by Module & Piscine</h3>
                        <div id="performance-analytics-chart" class="chart"></div>
                    </div>
                    <div class="chart-card">
                        <h3>XP Progress Over Time</h3>
                        <div id="xp-progress-chart" class="chart"></div>
                    </div>
                </div>
            </section>

        </main>

        <!-- Professional Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-info">
                    <h3>Zone01 Profile Dashboard</h3>
                    <p>Track your learning progress and achievements</p>
                </div>
                <div class="footer-credits">
                    <p>Developed by <strong>@steodhiambo</strong></p>
                    <p>&copy; 2025 Zone01 Kisumu</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Spinner -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Scripts -->
    <script src="auth.js"></script>
    <script src="graphql.js"></script>
    <script src="charts.js"></script>
    <script src="script.js"></script>
</body>
</html>
