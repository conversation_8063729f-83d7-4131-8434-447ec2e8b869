<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL Profile Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Test Page -->
    <div class="page active">
        <header class="header">
            <h1>GraphQL Profile Test</h1>
            <div class="header-buttons">
                <button id="load-demo-btn" class="demo-btn">Load Demo Data</button>
                <button id="clear-data-btn" class="test-btn">Clear Data</button>
            </div>
        </header>

        <main class="dashboard">
            <!-- User Info Section -->
            <section class="user-info">
                <h2>User Information</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h3>Basic Info</h3>
                        <div id="user-basic-info">
                            <p><strong>ID:</strong> <span id="user-id">-</span></p>
                            <p><strong>Login:</strong> <span id="user-login">-</span></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <h3>XP Summary</h3>
                        <div id="xp-summary">
                            <p><strong>Total XP:</strong> <span id="total-xp">-</span></p>
                            <p><strong>Projects Completed:</strong> <span id="projects-completed">-</span></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <h3>Progress Stats</h3>
                        <div id="progress-stats">
                            <p><strong>Pass Rate:</strong> <span id="pass-rate">-</span></p>
                            <p><strong>Audit Ratio:</strong> <span id="audit-ratio">-</span></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="statistics">
                <h2>Statistics & Analytics</h2>
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>Performance Analytics - XP Distribution by Piscine</h3>
                        <div id="performance-analytics-chart" class="chart"></div>
                    </div>
                    <div class="chart-card">
                        <h3>XP Progress Over Time</h3>
                        <div id="xp-progress-chart" class="chart"></div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="graphql.js"></script>
    <script src="charts.js"></script>
    <script>
        // Simplified test app
        class TestApp {
            constructor() {
                this.userData = null;
            }

            updateUserInfo() {
                console.log('Updating user info with data:', this.userData);
                
                if (!this.userData) {
                    console.log('No user data available');
                    return;
                }

                const user = this.userData.user;
                
                // Update basic info
                document.getElementById('user-id').textContent = user?.id || '-';
                document.getElementById('user-login').textContent = user?.login || '-';

                // Process XP data
                const transactions = this.userData.transactions || [];
                const xpData = graphqlClient.processXPData(transactions);
                document.getElementById('total-xp').textContent = xpData.totalXP.toLocaleString();

                // Calculate project stats
                const progress = this.userData.progress || [];
                const results = this.userData.results || [];
                const stats = graphqlClient.calculateStats(progress, results);
                document.getElementById('projects-completed').textContent = stats.passedProjects;
                document.getElementById('pass-rate').textContent = `${stats.passRate}%`;
                document.getElementById('audit-ratio').textContent = stats.auditRatio;
                
                console.log('User info updated successfully');
            }

            updateStatistics() {
                console.log('Updating statistics with data:', this.userData);
                
                if (!this.userData) {
                    console.log('No user data available for statistics');
                    return;
                }

                // Process data for charts
                const transactions = this.userData.transactions || [];
                const xpData = graphqlClient.processXPData(transactions);
                
                console.log('Processed XP data:', xpData);
                
                // Create performance analytics chart
                svgCharts.createPerformanceAnalyticsChart(
                    xpData.piscineXP, 
                    'performance-analytics-chart'
                );

                // Create XP progress chart
                svgCharts.createXPProgressChart(
                    xpData.xpProgress, 
                    'xp-progress-chart'
                );
                
                console.log('Statistics updated successfully');
            }

            loadDemoData() {
                const demoData = {
                    user: { id: 1, login: 'demo_user' },
                    transactions: [
                        { amount: 500, createdAt: '2023-01-15', path: '/piscine-go/quest-01' },
                        { amount: 750, createdAt: '2023-02-01', path: '/piscine-js/exercise-01' },
                        { amount: 300, createdAt: '2023-02-15', path: '/piscine-ux/project-01' },
                        { amount: 400, createdAt: '2023-03-01', path: '/piscine-ui/design-01' },
                        { amount: 600, createdAt: '2023-03-15', path: '/piscine-rust/basics-01' },
                        { amount: 800, createdAt: '2023-04-01', path: '/piscine-go/advanced-01' }
                    ],
                    progress: [
                        { grade: 1, path: '/piscine-go/quest-01' },
                        { grade: 1, path: '/piscine-js/exercise-01' },
                        { grade: 0, path: '/piscine-ux/project-01' },
                        { grade: 1, path: '/piscine-ui/design-01' }
                    ],
                    results: [
                        { grade: 1, object: { type: 'project' } },
                        { grade: 1, object: { type: 'project' } },
                        { grade: 0, object: { type: 'project' } }
                    ]
                };

                this.userData = demoData;
                this.updateUserInfo();
                this.updateStatistics();
                console.log('Demo data loaded');
            }

            clearData() {
                this.userData = null;
                
                // Clear UI
                document.getElementById('user-id').textContent = '-';
                document.getElementById('user-login').textContent = '-';
                document.getElementById('total-xp').textContent = '-';
                document.getElementById('projects-completed').textContent = '-';
                document.getElementById('pass-rate').textContent = '-';
                document.getElementById('audit-ratio').textContent = '-';
                
                // Clear charts
                document.getElementById('performance-analytics-chart').innerHTML = '';
                document.getElementById('xp-progress-chart').innerHTML = '';
                
                console.log('Data cleared');
            }
        }

        const testApp = new TestApp();

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('load-demo-btn').addEventListener('click', () => {
                testApp.loadDemoData();
            });
            
            document.getElementById('clear-data-btn').addEventListener('click', () => {
                testApp.clearData();
            });
        });
    </script>
</body>
</html>
