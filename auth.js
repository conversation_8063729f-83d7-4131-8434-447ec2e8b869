class AuthManager {
    constructor() {
        this.token = localStorage.getItem('jwt_token');
        this.apiUrl = 'https://learn.zone01kisumu.ke/api/auth/signin'; // Replace ((DOMAIN)) with actual domain
    }

    // Encode credentials for Basic authentication
    encodeCredentials(username, password) {
        return btoa(`${username}:${password}`);
    }

    // Login function
    async login(username, password) {
        try {
            const credentials = this.encodeCredentials(username, password);
            console.log('Attempting login with credentials for:', username);

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${credentials}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Login response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Login failed with response:', errorText);
                throw new Error('Invalid credentials');
            }

            const data = await response.text(); // JWT token is returned as text
            console.log('Raw token received:', data);

            // Clean the token - remove any whitespace or quotes
            this.token = data.trim().replace(/^["']|["']$/g, '');
            console.log('Cleaned token:', this.token);

            // Validate token format
            if (!this.isValidJWT(this.token)) {
                console.error('Invalid JWT format received');
                throw new Error('Invalid token format received from server');
            }

            localStorage.setItem('jwt_token', this.token);
            console.log('Token stored successfully');

            return true;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    // Logout function
    logout() {
        console.log('Logging out user...');
        this.token = null;
        localStorage.removeItem('jwt_token');

        // Clear any existing user data
        if (window.profileApp) {
            window.profileApp.userData = null;
        }

        this.showLoginPage();
    }

    // Check if user is authenticated
    isAuthenticated() {
        if (!this.token) {
            return false;
        }

        // Validate token format
        const cleanToken = this.getToken();
        if (!this.isValidJWT(cleanToken)) {
            console.log('Invalid JWT format, clearing token');
            this.logout();
            return false;
        }

        // Check if token is expired
        try {
            const decoded = this.decodeToken();
            if (decoded && decoded.exp) {
                const now = Math.floor(Date.now() / 1000);
                if (decoded.exp < now) {
                    console.log('Token expired, clearing token');
                    this.logout();
                    return false;
                }
            }
        } catch (error) {
            console.error('Error checking token expiration:', error);
            this.logout();
            return false;
        }

        return true;
    }

    // Get JWT token
    getToken() {
        // Clean token when retrieving it
        if (this.token) {
            return this.token.trim().replace(/^["']|["']$/g, '');
        }
        return this.token;
    }

    // Validate JWT format
    isValidJWT(token) {
        if (!token || typeof token !== 'string') {
            return false;
        }

        // JWT should have 3 parts separated by dots
        const parts = token.split('.');
        if (parts.length !== 3) {
            console.error('JWT should have 3 parts, got:', parts.length);
            return false;
        }

        // Check if each part is valid base64url
        for (let i = 0; i < parts.length; i++) {
            if (!this.isValidBase64Url(parts[i])) {
                console.error(`JWT part ${i + 1} is not valid base64url:`, parts[i]);
                return false;
            }
        }

        return true;
    }

    // Check if string is valid base64url
    isValidBase64Url(str) {
        if (!str || typeof str !== 'string') {
            return false;
        }

        // Base64url uses A-Z, a-z, 0-9, -, _ and no padding
        const base64urlRegex = /^[A-Za-z0-9_-]+$/;
        return base64urlRegex.test(str);
    }

    // Decode JWT to get user info
    decodeToken() {
        if (!this.token) return null;

        try {
            const cleanToken = this.getToken(); // Use cleaned token
            const parts = cleanToken.split('.');

            if (parts.length !== 3) {
                throw new Error('Invalid JWT format');
            }

            const payload = parts[1];

            // Convert base64url to base64
            const base64 = this.base64urlToBase64(payload);
            const decoded = JSON.parse(atob(base64));
            return decoded;
        } catch (error) {
            console.error('Error decoding token:', error);
            return null;
        }
    }

    // Convert base64url to base64
    base64urlToBase64(base64url) {
        // Replace base64url characters with base64 characters
        let base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');

        // Add padding if necessary
        while (base64.length % 4) {
            base64 += '=';
        }

        return base64;
    }

    // Get user ID from token
    getUserId() {
        const decoded = this.decodeToken();
        return decoded ? decoded.sub : null;
    }

    // Show login page
    showLoginPage() {
        document.getElementById('login-page').classList.add('active');
        document.getElementById('profile-page').classList.remove('active');
    }

    // Show profile page
    showProfilePage() {
        document.getElementById('login-page').classList.remove('active');
        document.getElementById('profile-page').classList.add('active');
    }

    // Initialize authentication state
    init() {
        if (this.isAuthenticated()) {
            this.showProfilePage();
            return true;
        } else {
            this.showLoginPage();
            return false;
        }
    }
}

// Create global auth manager instance
const authManager = new AuthManager();

// DOM event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Initialize auth state
    const isAuthenticated = authManager.init();
    
    // Login form handler
    const loginForm = document.getElementById('login-form');
    const loginError = document.getElementById('login-error');
    const loginBtn = document.getElementById('login-btn');
    
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Clear previous errors
        loginError.textContent = '';
        loginBtn.textContent = 'Logging in...';
        loginBtn.disabled = true;
        
        try {
            await authManager.login(username, password);
            authManager.showProfilePage();
            
            // Load profile data if authenticated
            if (window.loadProfileData) {
                window.loadProfileData();
            }
        } catch (error) {
            loginError.textContent = 'Invalid username/email or password';
        } finally {
            loginBtn.textContent = 'Login';
            loginBtn.disabled = false;
        }
    });
    
    // Logout button handler
    const logoutBtn = document.getElementById('logout-btn');
    logoutBtn.addEventListener('click', function() {
        authManager.logout();
    });



    // Load profile data if already authenticated
    if (isAuthenticated && window.loadProfileData) {
        console.log('User already authenticated, loading profile data...');
        window.loadProfileData();
    }
});
