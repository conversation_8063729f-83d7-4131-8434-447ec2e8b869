* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Login Page Styles */
.login-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-container h1 {
    color: white;
    margin-bottom: 30px;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

#login-form {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

#login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

#login-btn:hover {
    transform: translateY(-2px);
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    text-align: center;
    font-weight: 500;
}

/* Profile Page Styles */
.header {
    background: white;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #333;
    font-size: 2rem;
}

.header-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.logout-btn, .demo-btn, .test-btn {
    padding: 10px 20px;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
}

.logout-btn {
    background: #e74c3c;
}

.logout-btn:hover {
    background: #c0392b;
}

.demo-btn {
    background: #3498db;
}

.demo-btn:hover {
    background: #2980b9;
}

.test-btn {
    background: #27ae60;
}

.test-btn:hover {
    background: #229954;
}

.dashboard {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.user-info {
    margin-bottom: 40px;
}

.user-info h2 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.info-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.info-card h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.info-card p {
    margin-bottom: 8px;
    font-size: 1rem;
}

.statistics {
    margin-bottom: 40px;
}

.statistics h2 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 30px;
}

.chart-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.chart-card h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.chart {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Loading Styles */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 1000;
}

.loading.active {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* SVG Chart Styles */
.chart svg {
    max-width: 100%;
    height: auto;
}

.bar {
    transition: fill 0.3s ease;
}

.bar:hover {
    fill: #4a5568 !important;
}

.line {
    fill: none;
    stroke-width: 3;
    transition: stroke-width 0.3s ease;
}

.line:hover {
    stroke-width: 5;
}

.axis-label {
    font-family: 'Segoe UI', sans-serif;
    font-size: 12px;
    fill: #666;
}

.chart-title {
    font-family: 'Segoe UI', sans-serif;
    font-size: 16px;
    font-weight: 600;
    fill: #333;
}



/* Footer Styles */
.footer {
    background: #2c3e50;
    color: white;
    padding: 30px 0;
    margin-top: 40px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-info h3 {
    color: #ecf0f1;
    margin-bottom: 8px;
    font-size: 1.2rem;
}

.footer-info p {
    color: #bdc3c7;
    margin: 0;
    font-size: 0.9rem;
}

.footer-credits {
    text-align: right;
}

.footer-credits p {
    margin: 4px 0;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.footer-credits strong {
    color: #3498db;
    font-weight: 600;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .charts-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 15px;
    }

    .dashboard {
        padding: 25px;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header h1 {
        font-size: 1.5rem;
        margin: 0;
    }

    .charts-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .dashboard {
        padding: 20px 15px;
    }

    .chart {
        height: 300px;
    }

    .chart-card {
        padding: 20px;
    }

    .info-card {
        padding: 20px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .footer-credits {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 15px;
    }

    .login-container h1 {
        font-size: 2rem;
    }

    #login-form {
        padding: 30px 20px;
    }

    .dashboard {
        padding: 15px 10px;
    }

    .header {
        padding: 12px 15px;
    }

    .header h1 {
        font-size: 1.3rem;
    }

    .info-card, .chart-card {
        padding: 15px;
    }

    .info-card h3, .chart-card h3 {
        font-size: 1.1rem;
    }

    .chart {
        height: 250px;
    }

    .footer {
        padding: 20px 0;
    }

    .footer-content {
        padding: 0 15px;
    }

    .footer-info h3 {
        font-size: 1.1rem;
    }

    .footer-info p, .footer-credits p {
        font-size: 0.8rem;
    }
}
