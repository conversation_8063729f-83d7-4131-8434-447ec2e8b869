# GraphQL Profile Dashboard

A modern web application that displays your school profile data using GraphQL queries with interactive SVG charts.

## Features

- **JWT Authentication**: Secure login with username/email and password
- **GraphQL Integration**: Fetches data from the school's GraphQL API
- **Interactive SVG Charts**:
  1. **Performance Analytics**: Bar chart showing XP distribution across different piscines (Go, JS, UX, UI, Rust)
  2. **XP Progress**: Line chart displaying cumulative XP progress over time
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Data**: Displays current user statistics and progress

## Setup Instructions

### 1. Configure API Endpoints

Before using the application, you need to update the API endpoints in the following files:

**auth.js** (line 4):
```javascript
this.apiUrl = 'https://YOUR_DOMAIN/api/auth/signin';
```

**graphql.js** (line 4):
```javascript
this.endpoint = 'https://YOUR_DOMAIN/api/graphql-engine/v1/graphql';
```

Replace `YOUR_DOMAIN` with your actual school domain.

### 2. File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── auth.js            # Authentication management
├── graphql.js         # GraphQL client and queries
├── charts.js          # SVG chart generation
├── script.js          # Main application logic
└── README.md          # This file
```

### 3. Local Development

1. Clone or download the project files
2. Update the API endpoints as described above
3. Open `index.html` in a web browser or serve it using a local web server

**Using Python (recommended):**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**Using Node.js:**
```bash
npx http-server
```

**Using PHP:**
```bash
php -S localhost:8000
```

### 4. Hosting Options

The application can be hosted on various platforms:

- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **Netlify**: Drag and drop the files or connect to a Git repository
- **Vercel**: Deploy directly from Git or upload files
- **Firebase Hosting**: Use Firebase CLI to deploy

## Usage

1. **Login**: Enter your username/email and password
2. **View Profile**: See your basic information and statistics
3. **Explore Charts**: 
   - Hover over bars in the Performance Analytics chart to see XP values
   - Hover over points in the XP Progress chart to see dates and cumulative XP
4. **Logout**: Click the logout button to end your session

## GraphQL Queries

The application uses the following GraphQL queries:

- **User Information**: Basic user data (ID, login)
- **Transactions**: XP transactions with amounts and dates
- **Progress**: Project progress and grades
- **Results**: Project results and audit data

## Charts Details

### Performance Analytics Chart
- **Type**: Bar chart
- **X-axis**: Piscine types (Go, JS, UX, UI, Rust)
- **Y-axis**: XP distribution
- **Features**: Color-coded bars, hover tooltips, interactive elements

### XP Progress Chart
- **Type**: Line chart
- **X-axis**: Months (grouped by month)
- **Y-axis**: XP Points (cumulative)
- **Features**: Monthly progression, data points, hover tooltips with month labels

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Security Features

- JWT token storage in localStorage
- Automatic token validation
- Secure API communication with Bearer authentication
- Basic authentication for login endpoint

## Troubleshooting

### Common Issues

1. **Login fails**: Check if the domain is correctly configured in `auth.js`
2. **No data displayed**: Verify GraphQL endpoint in `graphql.js`
3. **Charts not showing**: Check browser console for JavaScript errors
4. **CORS errors**: Ensure the API allows requests from your domain

### Testing and Debugging

For testing and debugging, you can use these functions in the browser console:

**Test Authentication:**
```javascript
window.testAuth('your_username', 'your_password');
```

**Test GraphQL Query:**
```javascript
window.testGraphQLQuery();
```

**Debug App State:**
```javascript
window.debugApp();
```

**Manual Data Refresh:**
```javascript
profileApp.loadProfileData();
```

## Development

### Adding New Charts

1. Add a new method to the `SVGCharts` class in `charts.js`
2. Call the method from `updateStatistics()` in `script.js`
3. Add the corresponding HTML container in `index.html`

### Modifying GraphQL Queries

1. Update queries in `graphql.js`
2. Modify data processing methods as needed
3. Update the UI components in `script.js`

## License

This project is for educational purposes as part of the GraphQL learning curriculum.
