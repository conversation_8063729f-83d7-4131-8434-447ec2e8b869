class GraphQLClient {
    constructor() {
        this.endpoint = 'https://learn.zone01kisumu.ke/api/graphql-engine/v1/graphql'; // Replace ((DOMAIN)) with actual domain
    }

    // Make GraphQL query
    async query(query, variables = {}) {
        const token = authManager.getToken();
        console.log('Making GraphQL query with token available:', !!token);

        if (!token) {
            throw new Error('No authentication token available');
        }

        // Validate token format before using it
        if (!authManager.isValidJWT(token)) {
            console.error('Invalid JWT format detected');
            throw new Error('Invalid JWT token format');
        }

        try {
            console.log('Sending GraphQL query to:', this.endpoint);
            console.log('Query:', query.substring(0, 100) + '...');
            console.log('Token length:', token.length);
            console.log('Token parts:', token.split('.').length);

            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    query,
                    variables
                })
            });

            console.log('GraphQL response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('GraphQL HTTP error response:', errorText);

                // If it's an auth error, clear the token
                if (response.status === 401 || response.status === 403) {
                    console.log('Authentication error, clearing token');
                    authManager.logout();
                }

                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('GraphQL response data:', data);

            if (data.errors) {
                console.error('GraphQL errors:', data.errors);

                // Check if it's a JWT verification error
                const jwtError = data.errors.find(err =>
                    err.message && err.message.includes('JWT') ||
                    err.message && err.message.includes('verify')
                );

                if (jwtError) {
                    console.log('JWT verification error, clearing token');
                    authManager.logout();
                }

                throw new Error(data.errors[0].message);
            }

            return data.data;
        } catch (error) {
            console.error('GraphQL query error:', error);
            throw error;
        }
    }

    // Get user basic information including audits ratio
    async getUserInfo() {
        const query = `
            query {
                user {
                    id
                    login
                    auditRatio
                    totalUp
                    totalDown
                }
            }
        `;
        return this.query(query);
    }

    // Get user transactions (XP data)
    async getUserTransactions() {
        const query = `
            query {
                transaction(where: {type: {_eq: "xp"}}, order_by: {createdAt: asc}) {
                    id
                    type
                    amount
                    createdAt
                    path
                    object {
                        name
                        type
                    }
                }
            }
        `;
        return this.query(query);
    }

    // Get user progress data
    async getUserProgress() {
        const query = `
            query {
                progress {
                    id
                    grade
                    createdAt
                    path
                    object {
                        name
                        type
                    }
                }
            }
        `;
        return this.query(query);
    }

    // Get user results
    async getUserResults() {
        const query = `
            query {
                result {
                    id
                    grade
                    createdAt
                    path
                    object {
                        name
                        type
                    }
                }
            }
        `;
        return this.query(query);
    }



    // Get piscine-specific XP data for all piscines
    async getAllPiscinesXPData() {
        const query = `
            query {
                # Get Module XP
                moduleXP: transaction(
                    where: {
                        type: {_eq: "xp"},
                        path: {_ilike: "%module%"}
                    }
                ) {
                    id
                    amount
                    path
                    createdAt
                    object {
                        name
                        type
                    }
                }
                
                # Get Go Piscine XP
                goPiscineXP: transaction(
                    where: {
                        type: {_eq: "xp"},
                        _or: [
                            {path: {_ilike: "%piscine-go%"}},
                            {path: {_ilike: "%/go/%"}},
                            {object: {name: {_ilike: "%go%"}}}
                        ]
                    }
                ) {
                    id
                    amount
                    path
                    createdAt
                    object {
                        name
                        type
                    }
                }
                
                # Get JS Piscine XP
                jsPiscineXP: transaction(
                    where: {
                        type: {_eq: "xp"},
                        _or: [
                            {path: {_ilike: "%piscine-js%"}},
                            {path: {_ilike: "%/js/%"}},
                            {object: {name: {_ilike: "%js%"}}}
                        ]
                    }
                ) {
                    id
                    amount
                    path
                    createdAt
                    object {
                        name
                        type
                    }
                }
                
                # Get UX Piscine XP
                uxPiscineXP: transaction(
                    where: {
                        type: {_eq: "xp"},
                        _or: [
                            {path: {_ilike: "%piscine-ux%"}},
                            {path: {_ilike: "%/ux/%"}},
                            {object: {name: {_ilike: "%ux%"}}}
                        ]
                    }
                ) {
                    id
                    amount
                    path
                    createdAt
                    object {
                        name
                        type
                    }
                }
                
                # Get UI Piscine XP
                uiPiscineXP: transaction(
                    where: {
                        type: {_eq: "xp"},
                        _or: [
                            {path: {_ilike: "%piscine-ui%"}},
                            {path: {_ilike: "%/ui/%"}},
                            {object: {name: {_ilike: "%ui%"}}}
                        ]
                    }
                ) {
                    id
                    amount
                    path
                    createdAt
                    object {
                        name
                        type
                    }
                }
                
                # Get Rust Piscine XP
                rustPiscineXP: transaction(
                    where: {
                        type: {_eq: "xp"},
                        _or: [
                            {path: {_ilike: "%piscine-rust%"}},
                            {path: {_ilike: "%/rust/%"}},
                            {object: {name: {_ilike: "%rust%"}}}
                        ]
                    }
                ) {
                    id
                    amount
                    path
                    createdAt
                    object {
                        name
                        type
                    }
                }
            }
        `;
        return this.query(query);
    }

    // Get all user data for dashboard with detailed piscine breakdown
    async getAllUserData() {
        try {
            console.log('Fetching all user data...');

            const [userInfo, transactions, progress, results, piscineData] = await Promise.all([
                this.getUserInfo(),
                this.getUserTransactions(),
                this.getUserProgress(),
                this.getUserResults(),
                this.getAllPiscinesXPData()
            ]);



            const userData = {
                user: userInfo.user && userInfo.user.length > 0 ? userInfo.user[0] : null,
                transactions: transactions.transaction || [],
                progress: progress.progress || [],
                results: results.result || [],
                piscineData: piscineData // Store the detailed piscine data
            };

            console.log('Processed user data:', userData);
            return userData;
        } catch (error) {
            console.error('Error fetching user data:', error);
            throw error;
        }
    }

    // Process XP data for charts with real piscine data
    processXPData(transactions, piscineData) {
        console.log('Processing XP data for', transactions.length, 'transactions');

        // Initialize piscine XP object
        const piscineXP = {
            'Module': 0,
            'Piscine Go': 0,
            'Piscine JS': 0,
            'Piscine UX': 0,
            'Piscine UI': 0,
            'Piscine Rust': 0
        };

        // XP progress over time
        const xpProgress = [];
        let cumulativeXP = 0;

        // If we have detailed piscine data from the API, use it
        if (piscineData) {
            // Sum up XP for each piscine type
            if (piscineData.moduleXP) {
                piscineXP['Module'] = piscineData.moduleXP.reduce((sum, tx) => sum + tx.amount, 0);
            }

            if (piscineData.goPiscineXP) {
                piscineXP['Piscine Go'] = piscineData.goPiscineXP.reduce((sum, tx) => sum + tx.amount, 0);
            }

            if (piscineData.jsPiscineXP) {
                piscineXP['Piscine JS'] = piscineData.jsPiscineXP.reduce((sum, tx) => sum + tx.amount, 0);
            }

            if (piscineData.uxPiscineXP) {
                piscineXP['Piscine UX'] = piscineData.uxPiscineXP.reduce((sum, tx) => sum + tx.amount, 0);
            }

            if (piscineData.uiPiscineXP) {
                piscineXP['Piscine UI'] = piscineData.uiPiscineXP.reduce((sum, tx) => sum + tx.amount, 0);
            }

            if (piscineData.rustPiscineXP) {
                piscineXP['Piscine Rust'] = piscineData.rustPiscineXP.reduce((sum, tx) => sum + tx.amount, 0);
            }
        } else {
            // Fall back to the old method if detailed data isn't available

            // Sort transactions by date
            const sortedTransactions = [...transactions].sort((a, b) =>
                new Date(a.createdAt) - new Date(b.createdAt)
            );

            // Categorize transactions
            sortedTransactions.forEach(transaction => {
                const path = transaction.path ? transaction.path.toLowerCase() : '';
                const objectName = transaction.object && transaction.object.name ?
                    transaction.object.name.toLowerCase() : '';

                // Categorize transaction
                if (path.includes('module') || objectName.includes('module')) {
                    piscineXP['Module'] += transaction.amount;
                } else if (path.includes('piscine-go') || path.includes('/go/') ||
                          objectName.includes('go')) {
                    piscineXP['Piscine Go'] += transaction.amount;
                } else if (path.includes('piscine-js') || path.includes('/js/') ||
                          objectName.includes('js')) {
                    piscineXP['Piscine JS'] += transaction.amount;
                } else if (path.includes('piscine-ux') || path.includes('/ux/') ||
                          objectName.includes('ux')) {
                    piscineXP['Piscine UX'] += transaction.amount;
                } else if (path.includes('piscine-ui') || path.includes('/ui/') ||
                          objectName.includes('ui')) {
                    piscineXP['Piscine UI'] += transaction.amount;
                } else if (path.includes('piscine-rust') || path.includes('/rust/') ||
                          objectName.includes('rust')) {
                    piscineXP['Piscine Rust'] += transaction.amount;
                } else if (path.includes('piscine')) {
                    // Generic piscine
                    if (objectName.includes('go')) {
                        piscineXP['Piscine Go'] += transaction.amount;
                    } else if (objectName.includes('js')) {
                        piscineXP['Piscine JS'] += transaction.amount;
                    } else if (objectName.includes('ux')) {
                        piscineXP['Piscine UX'] += transaction.amount;
                    } else if (objectName.includes('ui')) {
                        piscineXP['Piscine UI'] += transaction.amount;
                    } else if (objectName.includes('rust')) {
                        piscineXP['Piscine Rust'] += transaction.amount;
                    } else {
                        // Default to Module for uncategorized
                        piscineXP['Module'] += transaction.amount;
                    }
                } else {
                    // Default to Module for uncategorized
                    piscineXP['Module'] += transaction.amount;
                }
            });
        }

        // Sort transactions by date for progress tracking
        const sortedTransactions = [...transactions].sort((a, b) =>
            new Date(a.createdAt) - new Date(b.createdAt)
        );

        // Track cumulative XP over time
        sortedTransactions.forEach(transaction => {
            cumulativeXP += transaction.amount;
            xpProgress.push({
                date: new Date(transaction.createdAt),
                xp: cumulativeXP
            });
        });



        return {
            piscineXP,
            xpProgress,
            totalXP: cumulativeXP
        };
    }

    // Calculate statistics
    calculateStats(progress, results) {
        const totalProjects = progress.length;
        const passedProjects = progress.filter(p => p.grade >= 1).length;
        const passRate = totalProjects > 0 ? (passedProjects / totalProjects * 100).toFixed(1) : 0;

        // Calculate total audits for display
        const auditResults = results.filter(r => r.object && r.object.type === 'project');
        const totalAudits = auditResults.length;

        return {
            totalProjects,
            passedProjects,
            passRate,
            totalAudits
        };
    }
}

// Create global GraphQL client instance
const graphqlClient = new GraphQLClient();

// Make the GraphQL client available globally
window.graphqlClient = graphqlClient;
